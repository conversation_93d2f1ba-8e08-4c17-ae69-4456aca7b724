version: '3.8'

services:
  # Frontend application
  pharmacy-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: pharmacy-frontend
    ports:
      - "3000:8080"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    networks:
      - pharmacy-network
    depends_on:
      - pharmacy-db
      - redis
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database
  pharmacy-db:
    image: postgres:15-alpine
    container_name: pharmacy-db
    environment:
      POSTGRES_DB: pharmacy_store
      POSTGRES_USER: pharmacy_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-pharmacy_secure_password}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - pharmacy-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pharmacy_user -d pharmacy_store"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: pharmacy-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_secure_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - pharmacy-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Backend API (placeholder - adjust based on your backend)
  pharmacy-api:
    image: node:20-alpine
    container_name: pharmacy-api
    working_dir: /app
    volumes:
      - ./api:/app  # Mount your API code here
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - DB_HOST=pharmacy-db
      - DB_PORT=5432
      - DB_NAME=pharmacy_store
      - DB_USER=pharmacy_user
      - DB_PASSWORD=${DB_PASSWORD:-pharmacy_secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_secure_password}
    command: ["sh", "-c", "npm install && npm start"]
    restart: unless-stopped
    networks:
      - pharmacy-network
    depends_on:
      - pharmacy-db
      - redis

  # Development frontend (for development environment)
  pharmacy-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: pharmacy-frontend-dev
    ports:
      - "9000:9000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - pharmacy-network
    profiles:
      - dev

  # Nginx reverse proxy (optional)
  nginx-proxy:
    image: nginx:1.25-alpine
    container_name: pharmacy-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
    restart: unless-stopped
    networks:
      - pharmacy-network
    depends_on:
      - pharmacy-frontend
      - pharmacy-api
    profiles:
      - proxy

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  pharmacy-network:
    driver: bridge
