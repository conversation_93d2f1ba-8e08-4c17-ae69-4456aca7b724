# Database Configuration
DB_PASSWORD=pharmacy_secure_password
DB_HOST=pharmacy-db
DB_PORT=5432
DB_NAME=pharmacy_store
DB_USER=pharmacy_user

# Redis Configuration
REDIS_PASSWORD=redis_secure_password
REDIS_HOST=redis
REDIS_PORT=6379

# Application Configuration
NODE_ENV=production
API_BASE_URL=http://pharmacy-api:8000

# Security
JWT_SECRET=your_jwt_secret_key_here
SESSION_SECRET=your_session_secret_here

# Optional: External services
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# Optional: File storage
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=your-bucket-name
