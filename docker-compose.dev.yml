version: '3.8'

# Development-specific docker-compose override
services:
  pharmacy-frontend:
    build:
      target: development
    ports:
      - "9000:9000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # For file watching in Docker
    command: ["npm", "run", "dev"]

  pharmacy-db:
    ports:
      - "5432:5432"  # Expose database port for development tools
    environment:
      POSTGRES_DB: pharmacy_store_dev
      POSTGRES_USER: pharmacy_dev
      POSTGRES_PASSWORD: dev_password

  redis:
    ports:
      - "6379:6379"  # Expose Redis port for development tools
    command: redis-server --appendonly yes  # No password for dev

  # Development database admin tool
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pharmacy-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - pharmacy-network
    depends_on:
      - pharmacy-db

  # Redis admin tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: pharmacy-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - pharmacy-network
    depends_on:
      - redis

volumes:
  pgadmin_data:
    driver: local
