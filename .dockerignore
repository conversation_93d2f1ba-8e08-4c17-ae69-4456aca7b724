# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Build outputs
dist
.quasar
/quasar.config.*.temporary.compiled*

# Environment files
.env
.env.local
.env.*.local
.env.production
.env.development

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~
.DS_Store
.thumbs.db

# Version control
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
*.md
docs/

# Testing
coverage/
.nyc_output
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Linting and formatting
.eslintrc*
.prettierrc*
.editorconfig

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
Jenkinsfile

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# OS generated files
Thumbs.db
ehthumbs.db

# Cordova related directories and files
/src-cordova/node_modules
/src-cordova/platforms
/src-cordova/plugins
/src-cordova/www

# Capacitor related directories and files
/src-capacitor/www
/src-capacitor/node_modules

# Local development files
*.local
