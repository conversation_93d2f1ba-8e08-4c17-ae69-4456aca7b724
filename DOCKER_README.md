# Docker Setup for Pharmacy Store Application

This document provides instructions for containerizing and running the Pharmacy Store application using Docker.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB of available RAM
- At least 10GB of available disk space

## Quick Start

### 1. Environment Setup

Copy the example environment file and customize it:

```bash
cp .env.example .env
```

Edit `.env` file with your preferred passwords and configuration.

### 2. Production Deployment

Build and start all services:

```bash
# Build and start in detached mode
docker-compose up -d

# View logs
docker-compose logs -f

# Check service status
docker-compose ps
```

The application will be available at:
- Frontend: http://localhost:3000
- API: http://localhost:8000
- Database: localhost:5432

### 3. Development Environment

For development with hot-reload:

```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Or use the development profile
docker-compose --profile dev up -d
```

Development tools will be available at:
- Frontend (dev): http://localhost:9000
- PgAdmin: http://localhost:5050 (<EMAIL> / admin)
- Redis Commander: http://localhost:8081

## File Structure

```
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Production services
├── docker-compose.dev.yml     # Development overrides
├── nginx.conf                 # Nginx configuration
├── .dockerignore             # Docker ignore patterns
├── .env.example              # Environment variables template
└── init-scripts/
    └── 01-init.sql           # Database initialization
```

## Services

### Frontend (pharmacy-frontend)
- **Image**: Custom built from Dockerfile
- **Port**: 3000 (production), 9000 (development)
- **Technology**: Quasar/Vue.js with Nginx

### Database (pharmacy-db)
- **Image**: postgres:15-alpine
- **Port**: 5432
- **Credentials**: Configured via environment variables

### Cache (redis)
- **Image**: redis:7-alpine
- **Port**: 6379
- **Authentication**: Password protected

### API (pharmacy-api)
- **Image**: node:20-alpine
- **Port**: 8000
- **Note**: Placeholder - replace with your actual API

## Docker Commands

### Basic Operations

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart a specific service
docker-compose restart pharmacy-frontend

# View logs
docker-compose logs -f [service-name]

# Execute commands in container
docker-compose exec pharmacy-frontend sh
```

### Development Commands

```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Rebuild frontend for development
docker-compose build pharmacy-frontend

# Install new npm packages
docker-compose exec pharmacy-frontend npm install [package-name]
```

### Database Operations

```bash
# Access database
docker-compose exec pharmacy-db psql -U pharmacy_user -d pharmacy_store

# Backup database
docker-compose exec pharmacy-db pg_dump -U pharmacy_user pharmacy_store > backup.sql

# Restore database
docker-compose exec -T pharmacy-db psql -U pharmacy_user pharmacy_store < backup.sql
```

## Environment Variables

Key environment variables in `.env`:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_PASSWORD` | PostgreSQL password | pharmacy_secure_password |
| `REDIS_PASSWORD` | Redis password | redis_secure_password |
| `NODE_ENV` | Application environment | production |
| `API_BASE_URL` | Backend API URL | http://pharmacy-api:8000 |

## Security Features

- Non-root user in containers
- Security headers in Nginx
- Password-protected Redis
- Scram-SHA-256 authentication for PostgreSQL
- Minimal base images (Alpine Linux)
- Health checks for all services

## Performance Optimizations

- Multi-stage Docker builds
- Nginx gzip compression
- Static asset caching
- Efficient layer caching
- Volume mounts for persistent data

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in docker-compose.yml
2. **Permission issues**: Check file ownership and Docker daemon
3. **Memory issues**: Increase Docker memory allocation
4. **Build failures**: Clear Docker cache with `docker system prune`

### Useful Commands

```bash
# Check container resource usage
docker stats

# Clean up unused resources
docker system prune -a

# View container details
docker inspect [container-name]

# Check network connectivity
docker-compose exec pharmacy-frontend ping pharmacy-db
```

## Production Considerations

1. **SSL/TLS**: Configure HTTPS with proper certificates
2. **Reverse Proxy**: Use the nginx-proxy service for load balancing
3. **Monitoring**: Add monitoring services (Prometheus, Grafana)
4. **Backups**: Implement automated database backups
5. **Secrets**: Use Docker secrets or external secret management
6. **Updates**: Implement rolling updates strategy

## Development Workflow

1. Make code changes in your local environment
2. Changes are automatically reflected (hot-reload in dev mode)
3. Test changes in the containerized environment
4. Build production image for deployment

## Support

For issues related to Docker setup, check:
- Docker logs: `docker-compose logs`
- Container status: `docker-compose ps`
- System resources: `docker system df`
